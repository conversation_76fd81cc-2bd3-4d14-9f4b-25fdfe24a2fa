# 双向CutMix可视化增强总结

## 修改概述

我已经成功修改了 `slic_uncertainty_cutmix_visualization_synapse.py` 文件，实现了双向CutMix可视化，添加了以下新功能：

### 1. 标签可视化功能
- **新增函数**：
  - `cutmix_labels()`: 对标签执行CutMix操作
  - `create_label_colormap()`: 创建Synapse数据集的9类标签颜色映射
  - `visualize_label_overlay()`: 创建图像和标签的叠加可视化

### 2. 超像素不确定度映射
- **新增函数**：
  - `create_uncertainty_superpixel_map()`: 创建超像素不确定度映射图，每个超像素区域显示其平均不确定度值，颜色越深表示不确定度越高

### 3. 双向CutMix实现
- **新增功能**：
  - 实现了双向CutMix操作：图像2→图像1 和 图像1→图像2
  - 使用不同颜色边缘区分两个方向：绿色边缘表示2→1，红色边缘表示1→2
  - 同时处理图像和标签的双向交换

### 4. 可视化布局增强
- **布局改进**：
  - 从原来的3行5列布局扩展为5行7列布局
  - 图像尺寸从20x12增加到28x20以容纳更多内容
  - 完整展示双向CutMix的过程和结果

### 5. 新增可视化内容

#### 第一行：原始数据和不确定度
1. Image 1 (Target) - 目标图像
2. SLIC Superpixels - 超像素分割
3. Image 2 (Source) - 源图像
4. SLIC Superpixels - 超像素分割
5. Entropy Uncertainty - 熵不确定度
6. **Superpixel Uncertainty** - 超像素不确定度映射（带白色边界线）

#### 第二行：标签可视化
1. Label 1 (Target) - 目标标签
2. Image 1 + Label Overlay - 图像1+标签叠加
3. Label 2 (Source) - 源标签
4. Image 2 + Label Overlay - 图像2+标签叠加
5. CutMix Label 1→2 - CutMix标签结果（2→1方向）
6. CutMix 1→2 + Label - CutMix结果+标签叠加（2→1方向）
7. CutMix 2→1 + Label - CutMix结果+标签叠加（1→2方向）

#### 第三行：双向CutMix过程
1. Selected High Uncertainty Superpixels - 选中的高不确定度超像素
2. Connected Regions Mask - 连通区域掩码
3. **Bidirectional CutMix** - 双向CutMix操作（绿色和红色箭头）
4. **CutMix 2→1 (Green Edge)** - CutMix结果1，绿色边缘
5. **CutMix 1→2 (Red Edge)** - CutMix结果2，红色边缘
6. Selected Regions Uncertainty - 选中区域的不确定度分布
7. **Bidirectional Comparison** - 双向结果对比

#### 第四行：双向对比
1-2. Image 1: Before vs After CutMix (2→1, Green) - 图像1前后对比
3-4. Image 2: Before vs After CutMix (1→2, Red) - 图像2前后对比
5. Label 1: Before vs After - 标签1前后对比
6. Label 2: Before vs After - 标签2前后对比

#### 第五行：统计信息
1-7. 双向CutMix统计信息（包含两个方向的标签统计）

### 5. 数据处理增强
- 添加了标签数据的加载和预处理
- 同时对图像和标签执行CutMix操作
- 增加了标签相关的统计信息

### 6. 颜色映射
- Synapse数据集9类标签的专用颜色映射：
  - 0: 背景 (黑色)
  - 1: 脾脏 (红色)
  - 2: 右肾 (绿色)
  - 3: 左肾 (蓝色)
  - 4: 胆囊 (黄色)
  - 5: 肝脏 (品红色)
  - 6: 胃 (青色)
  - 7: 主动脉 (灰色)
  - 8: 胰腺 (橙色)

### 7. 颜色编码系统
- **绿色边缘**：表示从图像2复制到图像1的区域（2→1方向）
- **红色边缘**：表示从图像1复制到图像2的区域（1→2方向）
- **白色边界线**：超像素分割边界，无空隙连续显示
- **viridis配色**：超像素不确定度主图
- **plasma配色**：选中区域不确定度图

### 8. 输出文件
- 图像保存为：`bidirectional_slic_uncertainty_cutmix_paper_figure.png`
- 数据保存为：`bidirectional_slic_cutmix_data.npz`

## 主要特色

1. **双向CutMix可视化**：完整展示两个方向的CutMix操作和结果
2. **颜色编码区分**：使用绿色和红色边缘清楚区分两个CutMix方向
3. **完整的CutMix流程可视化**：从原始数据到最终结果的完整展示
4. **标签感知的CutMix**：同时处理图像和对应的分割标签
5. **超像素级不确定度可视化**：直观显示每个超像素区域的不确定度，带连续边界线
6. **丰富的统计信息**：包含双向CutMix的图像、标签、不确定度详细统计
7. **论文级质量**：高分辨率输出，适合学术发表

## 使用方法

运行脚本：
```bash
python code/slic_uncertainty_cutmix_visualization_synapse.py
```

脚本将自动：
1. 加载预训练模型（如果可用）
2. 加载测试图像和标签
3. 计算不确定度
4. 执行SLIC超像素分割
5. 选择高不确定度区域
6. **执行双向CutMix操作**
7. 生成完整的可视化图像（包含颜色编码的边缘）
8. 保存结果和数据

## 双向CutMix的优势

1. **数据增强效果加倍**：一次操作生成两个增强样本
2. **对称性验证**：可以验证CutMix操作的对称性和一致性
3. **更丰富的训练数据**：提供更多样化的训练样本
4. **视觉对比清晰**：通过颜色编码清楚区分两个方向的操作

这个双向CutMix可视化版本提供了对CutMix过程更全面和直观的理解，特别适合研究双向数据增强在医学图像分割任务中的效果。
