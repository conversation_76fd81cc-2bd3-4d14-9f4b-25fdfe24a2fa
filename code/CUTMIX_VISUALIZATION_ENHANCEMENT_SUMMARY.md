# CutMix可视化增强总结

## 修改概述

我已经成功修改了 `slic_uncertainty_cutmix_visualization_synapse.py` 文件，添加了以下新功能：

### 1. 标签可视化功能
- **新增函数**：
  - `cutmix_labels()`: 对标签执行CutMix操作
  - `create_label_colormap()`: 创建Synapse数据集的9类标签颜色映射
  - `visualize_label_overlay()`: 创建图像和标签的叠加可视化

### 2. 超像素不确定度映射
- **新增函数**：
  - `create_uncertainty_superpixel_map()`: 创建超像素不确定度映射图，每个超像素区域显示其平均不确定度值，颜色越深表示不确定度越高

### 3. 可视化布局增强
- **布局改进**：
  - 从原来的3行5列布局扩展为4行6列布局
  - 图像尺寸从20x12增加到24x16以容纳更多内容

### 4. 新增可视化内容

#### 第一行：原始数据和不确定度
1. Image 1 (Target) - 目标图像
2. SLIC Superpixels - 超像素分割
3. Image 2 (Source) - 源图像  
4. SLIC Superpixels - 超像素分割
5. Entropy Uncertainty - 熵不确定度
6. **Superpixel Uncertainty** - 超像素不确定度映射（新增）

#### 第二行：标签可视化（新增整行）
1. Label 1 (Target) - 目标标签
2. Image 1 + Label Overlay - 图像1+标签叠加
3. Label 2 (Source) - 源标签
4. Image 2 + Label Overlay - 图像2+标签叠加
5. CutMix Label Result - CutMix标签结果
6. CutMix Result + Label - CutMix结果+标签叠加

#### 第三行：选择过程
1. Selected High Uncertainty Superpixels - 选中的高不确定度超像素
2. Connected Regions Mask - 连通区域掩码
3. Regions to Copy - 要复制的区域
4. CutMix Operation - CutMix操作箭头
5. CutMix Result - CutMix结果
6. **Selected Regions Uncertainty** - 选中区域的不确定度分布（新增）

#### 第四行：对比和统计
1-2. Image: Before vs After CutMix - 图像前后对比
3. Label: Before vs After - 标签前后对比
4-6. 统计信息（包含标签统计）

### 5. 数据处理增强
- 添加了标签数据的加载和预处理
- 同时对图像和标签执行CutMix操作
- 增加了标签相关的统计信息

### 6. 颜色映射
- Synapse数据集9类标签的专用颜色映射：
  - 0: 背景 (黑色)
  - 1: 脾脏 (红色)
  - 2: 右肾 (绿色)
  - 3: 左肾 (蓝色)
  - 4: 胆囊 (黄色)
  - 5: 肝脏 (品红色)
  - 6: 胃 (青色)
  - 7: 主动脉 (灰色)
  - 8: 胰腺 (橙色)

### 7. 输出文件
- 图像保存为：`slic_uncertainty_cutmix_with_labels_paper_figure.png`
- 数据保存为：`slic_cutmix_with_labels_data.npz`

## 主要特色

1. **完整的CutMix流程可视化**：从原始数据到最终结果的完整展示
2. **标签感知的CutMix**：同时处理图像和对应的分割标签
3. **超像素级不确定度可视化**：直观显示每个超像素区域的不确定度
4. **丰富的统计信息**：包含图像、标签、不确定度的详细统计
5. **论文级质量**：高分辨率输出，适合学术发表

## 使用方法

运行脚本：
```bash
python code/slic_uncertainty_cutmix_visualization_synapse.py
```

脚本将自动：
1. 加载预训练模型（如果可用）
2. 加载测试图像和标签
3. 计算不确定度
4. 执行SLIC超像素分割
5. 选择高不确定度区域
6. 执行CutMix操作
7. 生成完整的可视化图像
8. 保存结果和数据

这个增强版本提供了对CutMix过程更全面和直观的理解，特别是在医学图像分割任务中标签变化的可视化。
