import argparse
import numpy as np
import torch
import torch.nn.functional as F
import h5py
from scipy.ndimage import zoom
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
from skimage.segmentation import slic, mark_boundaries
from skimage.measure import label, regionprops
from skimage.morphology import binary_dilation, disk
import cv2

from networks.net_factory import BCP_net

def compute_entropy(predictions):
    """计算预测的熵值作为不确定性度量"""
    epsilon = 1e-8
    predictions = predictions + epsilon
    entropy = -torch.sum(predictions * torch.log(predictions), dim=1)
    return entropy

def monte_carlo_dropout(model, image, num_samples=20):
    """使用Monte Carlo Dropout估计不确定性"""
    model.train()  # 启用dropout
    predictions = []
    
    with torch.no_grad():
        for _ in range(num_samples):
            pred = model(image)
            pred = torch.softmax(pred, dim=1)
            predictions.append(pred.cpu())
    
    predictions = torch.stack(predictions)
    mean_prediction = torch.mean(predictions, dim=0)
    entropy_uncertainty = compute_entropy(mean_prediction)
    
    return mean_prediction, entropy_uncertainty

def get_slic_superpixels(image, n_segments=30, compactness=0.1):
    """对图像进行SLIC超像素分割"""
    # 确保图像是2D的
    if len(image.shape) == 3:
        image = image.squeeze()
    
    # 归一化到0-1范围
    image_norm = (image - image.min()) / (image.max() - image.min())
    
    # 进行SLIC分割
    segments = slic(image_norm, n_segments=n_segments, compactness=compactness, 
                   start_label=1, channel_axis=None)
    
    return segments

def select_high_uncertainty_regions(uncertainty_map, segments, top_k=5):
    """选择不确定性最高的连通超像素区域"""
    # 计算每个超像素的平均不确定性
    segment_uncertainties = []
    unique_segments = np.unique(segments)
    
    for seg_id in unique_segments:
        if seg_id == 0:  # 跳过背景
            continue
        mask = segments == seg_id
        avg_uncertainty = uncertainty_map[mask].mean()
        segment_uncertainties.append((seg_id, avg_uncertainty))
    
    # 按不确定性排序
    segment_uncertainties.sort(key=lambda x: x[1], reverse=True)
    
    # 选择top_k个最不确定的区域
    selected_segments = []
    for i in range(min(top_k, len(segment_uncertainties))):
        seg_id, uncertainty = segment_uncertainties[i]
        selected_segments.append(seg_id)
    
    # 创建选中区域的掩码
    selected_mask = np.zeros_like(segments, dtype=bool)
    for seg_id in selected_segments:
        selected_mask |= (segments == seg_id)
    
    return selected_mask, selected_segments

def ensure_connectivity(mask, min_size=50):
    """确保选中的区域是连通的，移除过小的区域"""
    # 标记连通组件
    labeled_mask = label(mask)
    
    # 获取区域属性
    regions = regionprops(labeled_mask)
    
    # 保留足够大的连通区域
    final_mask = np.zeros_like(mask, dtype=bool)
    for region in regions:
        if region.area >= min_size:
            final_mask[labeled_mask == region.label] = True
    
    return final_mask

def cutmix_operation(image1, image2, mask):
    """执行CutMix操作"""
    result = image1.copy()
    result[mask] = image2[mask]
    return result

def cutmix_labels(label1, label2, mask):
    """对标签执行CutMix操作"""
    result = label1.copy()
    result[mask] = label2[mask]
    return result

def create_label_colormap():
    """创建Synapse数据集的标签颜色映射"""
    # Synapse数据集有9个类别（包括背景）
    colors = [
        [0, 0, 0],        # 0: 背景 (黑色)
        [255, 0, 0],      # 1: 脾脏 (红色)
        [0, 255, 0],      # 2: 右肾 (绿色)
        [0, 0, 255],      # 3: 左肾 (蓝色)
        [255, 255, 0],    # 4: 胆囊 (黄色)
        [255, 0, 255],    # 5: 肝脏 (品红色)
        [0, 255, 255],    # 6: 胃 (青色)
        [128, 128, 128],  # 7: 主动脉 (灰色)
        [255, 165, 0],    # 8: 胰腺 (橙色)
    ]
    return ListedColormap(np.array(colors) / 255.0)

def visualize_label_overlay(image, label, alpha=0.3):
    """创建图像和标签的叠加可视化"""
    # 归一化图像到0-1
    image_norm = (image - image.min()) / (image.max() - image.min())

    # 创建RGB图像
    image_rgb = np.stack([image_norm, image_norm, image_norm], axis=-1)

    # 创建标签的颜色映射
    colormap = create_label_colormap()
    label_colored = colormap(label)[:, :, :3]  # 去掉alpha通道

    # 创建掩码，只在有标签的地方叠加颜色
    mask = label > 0

    # 叠加
    result = image_rgb.copy()
    result[mask] = (1 - alpha) * image_rgb[mask] + alpha * label_colored[mask]

    return result

def create_paper_figure():
    """创建适合论文的高质量可视化图"""
    # 检查CUDA是否可用
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 加载模型
    model = BCP_net(in_chns=1, class_num=9)
    model = model.to(device)
    
    # 尝试加载模型权重
    try:
        state = torch.load('model/Synapse/SUMix_fix_u_weight0.5_temperature0.7_2_labeled/self_train/iter_28500_dice_0.6815.pth', 
                          map_location=device)
        model.load_state_dict(state)
        print("Model loaded successfully!")
    except FileNotFoundError:
        print("Model file not found, using random weights for demonstration")
    
    # 加载两个图像和标签
    print("Loading images and labels...")

    # 图像1和标签1
    # h5f1 = h5py.File("code/case0022_slice063.h5", 'r')
    h5f1 = h5py.File("code/case0028_slice046.h5", 'r')
    image1 = h5f1['image'][:]
    label1 = h5f1['label'][:]
    h5f1.close()

    # 图像2和标签2
    # h5f2 = h5py.File("code/case0028_slice060.h5", 'r')
    h5f2 = h5py.File("code/case0028_slice060.h5", 'r')
    image2 = h5f2['image'][:]
    label2 = h5f2['label'][:]
    h5f2.close()
    
    # 预处理图像和标签
    def preprocess_image(img):
        x, y = img.shape
        img_resized = zoom(img, (256 / x, 256 / y), order=0)
        img_norm = (img_resized - img_resized.min()) / (img_resized.max() - img_resized.min())
        return img_norm

    def preprocess_label(lbl):
        x, y = lbl.shape
        lbl_resized = zoom(lbl, (256 / x, 256 / y), order=0)
        return lbl_resized.astype(np.uint8)

    image1_processed = preprocess_image(image1)
    image2_processed = preprocess_image(image2)
    label1_processed = preprocess_label(label1)
    label2_processed = preprocess_label(label2)
    
    print("Computing uncertainty for image2...")
    
    # 计算图像2的不确定性
    image2_tensor = torch.from_numpy(image2_processed.astype(np.float32)).unsqueeze(0).unsqueeze(0).to(device)
    _, uncertainty2 = monte_carlo_dropout(model, image2_tensor, num_samples=15)
    uncertainty2_np = uncertainty2.squeeze().numpy()
    
    print("Performing SLIC superpixel segmentation...")
    
    # 对两个图像进行SLIC分割
    segments1 = get_slic_superpixels(image1_processed, n_segments=60, compactness=0.1)
    segments2 = get_slic_superpixels(image2_processed, n_segments=60, compactness=0.1)
    
    print("Selecting high uncertainty regions...")
    
    # 选择图像2中不确定性最高的区域
    high_uncertainty_mask, selected_segments = select_high_uncertainty_regions(
        uncertainty2_np, segments2, top_k=3
    )
    
    # 确保连通性
    # final_mask = ensure_connectivity(high_uncertainty_mask, min_size=30)
    final_mask = high_uncertainty_mask
    
    # 执行CutMix（图像和标签）
    cutmix_result = cutmix_operation(image1_processed, image2_processed, final_mask)
    cutmix_label = cutmix_labels(label1_processed, label2_processed, final_mask)
    
    print("Creating visualization...")
    
    # 创建论文级别的可视化
    fig = plt.figure(figsize=(20, 16))

    # 设置整体布局 - 增加到4行来展示标签
    gs = fig.add_gridspec(4, 5, height_ratios=[1, 1, 1, 1], width_ratios=[1, 1, 1, 1, 1],
                         hspace=0.3, wspace=0.2)
    
    # 第一行：原始图像和SLIC分割
    ax1 = fig.add_subplot(gs[0, 0])
    ax1.imshow(image1_processed, cmap='gray')
    ax1.set_title('Image 1 (Target)', fontsize=14, fontweight='bold')
    ax1.axis('off')
    
    ax2 = fig.add_subplot(gs[0, 1])
    ax2.imshow(mark_boundaries(image1_processed, segments1, color=(1, 0, 0), mode='thick'))
    ax2.set_title('SLIC Superpixels', fontsize=14, fontweight='bold')
    ax2.axis('off')
    
    ax3 = fig.add_subplot(gs[0, 2])
    ax3.imshow(image2_processed, cmap='gray')
    ax3.set_title('Image 2 (Source)', fontsize=14, fontweight='bold')
    ax3.axis('off')
    
    ax4 = fig.add_subplot(gs[0, 3])
    ax4.imshow(mark_boundaries(image2_processed, segments2, color=(1, 0, 0), mode='thick'))
    ax4.set_title('SLIC Superpixels', fontsize=14, fontweight='bold')
    ax4.axis('off')
    
    ax5 = fig.add_subplot(gs[0, 4])
    im_unc = ax5.imshow(uncertainty2_np, cmap='hot', interpolation='bilinear')
    ax5.set_title('Entropy Uncertainty', fontsize=14, fontweight='bold')
    ax5.axis('off')
    cbar = plt.colorbar(im_unc, ax=ax5, shrink=0.8)
    cbar.set_label('Uncertainty', fontsize=12)

    # 第二行：标签可视化
    colormap = create_label_colormap()

    ax6 = fig.add_subplot(gs[1, 0])
    ax6.imshow(label1_processed, cmap=colormap, vmin=0, vmax=8)
    ax6.set_title('Label 1 (Target)', fontsize=14, fontweight='bold')
    ax6.axis('off')

    ax7 = fig.add_subplot(gs[1, 1])
    label1_overlay = visualize_label_overlay(image1_processed, label1_processed)
    ax7.imshow(label1_overlay)
    ax7.set_title('Image 1 + Label Overlay', fontsize=14, fontweight='bold')
    ax7.axis('off')

    ax8 = fig.add_subplot(gs[1, 2])
    ax8.imshow(label2_processed, cmap=colormap, vmin=0, vmax=8)
    ax8.set_title('Label 2 (Source)', fontsize=14, fontweight='bold')
    ax8.axis('off')

    ax9 = fig.add_subplot(gs[1, 3])
    label2_overlay = visualize_label_overlay(image2_processed, label2_processed)
    ax9.imshow(label2_overlay)
    ax9.set_title('Image 2 + Label Overlay', fontsize=14, fontweight='bold')
    ax9.axis('off')

    ax10 = fig.add_subplot(gs[1, 4])
    ax10.imshow(cutmix_label, cmap=colormap, vmin=0, vmax=8)
    ax10.set_title('CutMix Label Result', fontsize=14, fontweight='bold')
    ax10.axis('off')

    # 第三行：选择过程
    ax11 = fig.add_subplot(gs[2, 0])
    # 显示选中的超像素
    selected_vis = np.zeros_like(segments2)
    for seg_id in selected_segments:
        selected_vis[segments2 == seg_id] = 1
    ax11.imshow(image2_processed, cmap='gray', alpha=0.7)
    ax11.imshow(selected_vis, cmap='Reds', alpha=0.6)
    ax11.set_title('Selected High\nUncertainty Superpixels', fontsize=14, fontweight='bold')
    ax11.axis('off')

    ax12 = fig.add_subplot(gs[2, 1])
    ax12.imshow(final_mask, cmap='Reds')
    ax12.set_title('Connected Regions\nMask', fontsize=14, fontweight='bold')
    ax12.axis('off')

    ax13 = fig.add_subplot(gs[2, 2])
    # 显示要复制的区域
    copy_region = image2_processed.copy()
    copy_region[~final_mask] = 0
    ax13.imshow(copy_region, cmap='gray')
    ax13.set_title('Regions to Copy', fontsize=14, fontweight='bold')
    ax13.axis('off')

    # 添加箭头指示
    ax14 = fig.add_subplot(gs[2, 3])
    ax14.text(0.5, 0.5, '→', fontsize=60, ha='center', va='center',
             transform=ax14.transAxes, fontweight='bold', color='blue')
    ax14.set_title('CutMix\nOperation', fontsize=14, fontweight='bold')
    ax14.axis('off')

    ax15 = fig.add_subplot(gs[2, 4])
    ax15.imshow(cutmix_result, cmap='gray')
    ax15.contour(final_mask, levels=[0.5], colors='red', linewidths=2)
    ax15.set_title('CutMix Result', fontsize=14, fontweight='bold')
    ax15.axis('off')
    
    # 第四行：对比和统计
    ax16 = fig.add_subplot(gs[3, :2])
    # 并排显示原图和结果（图像）
    comparison = np.hstack([image1_processed, cutmix_result])
    ax16.imshow(comparison, cmap='gray')
    ax16.axvline(x=256, color='red', linestyle='--', linewidth=2)
    ax16.set_title('Image: Before (Left) vs After (Right) CutMix', fontsize=14, fontweight='bold')
    ax16.axis('off')

    # 标签对比
    ax17 = fig.add_subplot(gs[3, 2])
    label_comparison = np.hstack([label1_processed, cutmix_label])
    ax17.imshow(label_comparison, cmap=colormap, vmin=0, vmax=8)
    ax17.axvline(x=256, color='white', linestyle='--', linewidth=2)
    ax17.set_title('Label: Before vs After', fontsize=14, fontweight='bold')
    ax17.axis('off')

    # 统计信息
    ax18 = fig.add_subplot(gs[3, 3:])
    # 计算标签统计
    unique_labels1 = np.unique(label1_processed)
    unique_labels2 = np.unique(label2_processed)
    unique_labels_cutmix = np.unique(cutmix_label)
    copied_labels = np.unique(label2_processed[final_mask])

    stats_text = f"""CutMix Statistics:

Image & Uncertainty:
• Total superpixels in Image 2: {len(np.unique(segments2)) - 1}
• Selected high uncertainty superpixels: {len(selected_segments)}
• Final connected regions: {len(np.unique(label(final_mask))) - 1}
• Copied area: {final_mask.sum()} pixels ({100*final_mask.sum()/final_mask.size:.1f}%)
• Avg uncertainty in copied regions: {uncertainty2_np[final_mask].mean():.4f}

Label Information:
• Label 1 classes: {len(unique_labels1)} ({list(unique_labels1)})
• Label 2 classes: {len(unique_labels2)} ({list(unique_labels2)})
• CutMix result classes: {len(unique_labels_cutmix)} ({list(unique_labels_cutmix)})
• Copied label classes: {len(copied_labels)} ({list(copied_labels)})

Process:
1. Load images and corresponding segmentation labels
2. Perform SLIC superpixel segmentation on both images
3. Compute entropy-based uncertainty for Image 2
4. Select top-k superpixels with highest uncertainty
5. Apply CutMix to both images and labels simultaneously"""

    ax18.text(0.05, 0.95, stats_text, transform=ax18.transAxes, fontsize=10,
              verticalalignment='top', fontfamily='monospace',
              bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))
    ax18.axis('off')
    
    plt.suptitle('SLIC-based Uncertainty-guided CutMix for Medical Image Segmentation with Labels',
                 fontsize=18, fontweight='bold', y=0.98)

    # 保存高质量图像
    output_path = 'slic_uncertainty_cutmix_with_labels_paper_figure.png'
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"Paper figure saved to: {output_path}")

    # 保存数据
    cutmix_data = {
        'image1_original': image1_processed,
        'image2_original': image2_processed,
        'label1_original': label1_processed,
        'label2_original': label2_processed,
        'uncertainty_map': uncertainty2_np,
        'segments1': segments1,
        'segments2': segments2,
        'selected_mask': final_mask,
        'cutmix_result': cutmix_result,
        'cutmix_label': cutmix_label,
        'selected_segments': selected_segments
    }

    np.savez('slic_cutmix_with_labels_data.npz', **cutmix_data)
    print("CutMix data with labels saved to: slic_cutmix_with_labels_data.npz")
    
    plt.show()

if __name__ == "__main__":
    create_paper_figure()
