import argparse
import numpy as np
import torch
import torch.nn.functional as F
import h5py
from scipy.ndimage import zoom
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import ListedColormap
from skimage.segmentation import slic, mark_boundaries
from skimage.measure import label, regionprops
from skimage.morphology import binary_dilation, disk
import cv2
import os

from networks.net_factory import BCP_net

def compute_entropy(predictions):
    """计算预测的熵值作为不确定性度量"""
    epsilon = 1e-8
    predictions = predictions + epsilon
    entropy = -torch.sum(predictions * torch.log(predictions), dim=1)
    return entropy

def monte_carlo_dropout(model, image, num_samples=20):
    """使用Monte Carlo Dropout估计不确定性"""
    model.train()  # 启用dropout
    predictions = []
    
    with torch.no_grad():
        for _ in range(num_samples):
            pred = model(image)
            pred = torch.softmax(pred, dim=1)
            predictions.append(pred.cpu())
    
    predictions = torch.stack(predictions)
    mean_prediction = torch.mean(predictions, dim=0)
    entropy_uncertainty = compute_entropy(mean_prediction)
    
    return mean_prediction, entropy_uncertainty

def get_slic_superpixels(image, n_segments=30, compactness=0.1):
    """对图像进行SLIC超像素分割"""
    # 确保图像是2D的
    if len(image.shape) == 3:
        image = image.squeeze()
    
    # 归一化到0-1范围
    image_norm = (image - image.min()) / (image.max() - image.min())
    
    # 进行SLIC分割
    segments = slic(image_norm, n_segments=n_segments, compactness=compactness, 
                   start_label=1, channel_axis=None)
    
    return segments

def select_high_uncertainty_regions(uncertainty_map, segments, top_k=5):
    """选择不确定性最高的连通超像素区域"""
    # 计算每个超像素的平均不确定性
    segment_uncertainties = []
    unique_segments = np.unique(segments)
    
    for seg_id in unique_segments:
        if seg_id == 0:  # 跳过背景
            continue
        mask = segments == seg_id
        avg_uncertainty = uncertainty_map[mask].mean()
        segment_uncertainties.append((seg_id, avg_uncertainty))
    
    # 按不确定性排序
    segment_uncertainties.sort(key=lambda x: x[1], reverse=True)
    
    # 选择top_k个最不确定的区域
    selected_segments = []
    for i in range(min(top_k, len(segment_uncertainties))):
        seg_id, uncertainty = segment_uncertainties[i]
        selected_segments.append(seg_id)
    
    # 创建选中区域的掩码
    selected_mask = np.zeros_like(segments, dtype=bool)
    for seg_id in selected_segments:
        selected_mask |= (segments == seg_id)
    
    return selected_mask, selected_segments

def ensure_connectivity(mask, min_size=50):
    """确保选中的区域是连通的，移除过小的区域"""
    # 标记连通组件
    labeled_mask = label(mask)
    
    # 获取区域属性
    regions = regionprops(labeled_mask)
    
    # 保留足够大的连通区域
    final_mask = np.zeros_like(mask, dtype=bool)
    for region in regions:
        if region.area >= min_size:
            final_mask[labeled_mask == region.label] = True
    
    return final_mask

def cutmix_operation(image1, image2, mask):
    """执行CutMix操作"""
    result = image1.copy()
    result[mask] = image2[mask]
    return result

def cutmix_labels(label1, label2, mask):
    """对标签执行CutMix操作"""
    result = label1.copy()
    result[mask] = label2[mask]
    return result

def create_label_colormap():
    """创建Synapse数据集的标签颜色映射"""
    # Synapse数据集有9个类别（包括背景）
    colors = [
        [0, 0, 0],        # 0: 背景 (黑色)
        [255, 0, 0],      # 1: 脾脏 (红色)
        [0, 255, 0],      # 2: 右肾 (绿色)
        [0, 0, 255],      # 3: 左肾 (蓝色)
        [255, 255, 0],    # 4: 胆囊 (黄色)
        [255, 0, 255],    # 5: 肝脏 (品红色)
        [0, 255, 255],    # 6: 胃 (青色)
        [128, 128, 128],  # 7: 主动脉 (灰色)
        [255, 165, 0],    # 8: 胰腺 (橙色)
    ]
    return ListedColormap(np.array(colors) / 255.0)

def visualize_label_overlay(image, label, alpha=0.3):
    """创建图像和标签的叠加可视化"""
    # 归一化图像到0-1
    image_norm = (image - image.min()) / (image.max() - image.min())

    # 创建RGB图像
    image_rgb = np.stack([image_norm, image_norm, image_norm], axis=-1)

    # 创建标签的颜色映射
    colormap = create_label_colormap()
    label_colored = colormap(label)[:, :, :3]  # 去掉alpha通道

    # 创建掩码，只在有标签的地方叠加颜色
    mask = label > 0

    # 叠加
    result = image_rgb.copy()
    result[mask] = (1 - alpha) * image_rgb[mask] + alpha * label_colored[mask]

    return result

def create_uncertainty_superpixel_map(uncertainty_map, segments):
    """创建超像素不确定度映射图"""
    # 计算每个超像素的平均不确定性
    uncertainty_superpixel = np.zeros_like(segments, dtype=np.float32)
    unique_segments = np.unique(segments)

    for seg_id in unique_segments:
        if seg_id == 0:  # 跳过背景
            continue
        mask = segments == seg_id
        avg_uncertainty = uncertainty_map[mask].mean()
        uncertainty_superpixel[mask] = avg_uncertainty

    return uncertainty_superpixel

def save_image_only(image, mask, edge_color, frame_color, filename):
    """保存纯图片，不包含标题和坐标轴"""
    fig, ax = plt.subplots(figsize=(6, 6))
    ax.imshow(image, cmap='gray')
    ax.contour(mask, levels=[0.5], colors=edge_color, linewidths=3)

    # 设置边框颜色
    for spine in ax.spines.values():
        spine.set_edgecolor(frame_color)
        spine.set_linewidth(5)
        spine.set_visible(True)

    # 移除坐标轴和标题
    ax.set_xticks([])
    ax.set_yticks([])
    ax.axis('off')

    # 保存时去除所有边距和空白
    plt.subplots_adjust(left=0, right=1, top=1, bottom=0)
    plt.savefig(filename, dpi=300, bbox_inches='tight', pad_inches=0, facecolor='white')
    plt.close(fig)
    print(f"Image saved to: {filename}")

def save_simple_image(image, filename, cmap='gray'):
    """保存简单图片，不包含任何装饰"""
    fig, ax = plt.subplots(figsize=(6, 6))
    ax.imshow(image, cmap=cmap)
    ax.axis('off')
    plt.subplots_adjust(left=0, right=1, top=1, bottom=0)
    plt.savefig(filename, dpi=300, bbox_inches='tight', pad_inches=0, facecolor='white')
    plt.close(fig)
    print(f"Image saved to: {filename}")

def save_uncertainty_image(uncertainty_map, filename):
    """保存不确定性图"""
    fig, ax = plt.subplots(figsize=(6, 6))
    im = ax.imshow(uncertainty_map, cmap='hot', interpolation='bilinear')
    ax.axis('off')
    plt.subplots_adjust(left=0, right=1, top=1, bottom=0)
    plt.savefig(filename, dpi=300, bbox_inches='tight', pad_inches=0, facecolor='white')
    plt.close(fig)
    print(f"Uncertainty image saved to: {filename}")

def save_superpixel_image(image, segments, filename):
    """保存超像素分割图"""
    fig, ax = plt.subplots(figsize=(6, 6))
    ax.imshow(mark_boundaries(image, segments, color=(1, 0, 0), mode='thick'))
    ax.axis('off')
    plt.subplots_adjust(left=0, right=1, top=1, bottom=0)
    plt.savefig(filename, dpi=300, bbox_inches='tight', pad_inches=0, facecolor='white')
    plt.close(fig)
    print(f"Superpixel image saved to: {filename}")

def create_paper_figure():
    """创建适合论文的高质量可视化图"""
    # 检查CUDA是否可用
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 加载模型
    model = BCP_net(in_chns=1, class_num=9)
    model = model.to(device)
    
    # 尝试加载模型权重
    try:
        state = torch.load('model/Synapse/SUMix_fix_u_weight0.5_temperature0.7_2_labeled/self_train/iter_28500_dice_0.6815.pth', 
                          map_location=device)
        model.load_state_dict(state)
        print("Model loaded successfully!")
    except FileNotFoundError:
        print("Model file not found, using random weights for demonstration")
    
    # 加载两个图像和标签
    print("Loading images and labels...")

    # 图像1和标签1
    h5f1 = h5py.File("code/case0028_slice046.h5", 'r')
    # h5f1 = h5py.File("code/case0028_slice046.h5", 'r')
    image1 = h5f1['image'][:]
    label1 = h5f1['label'][:]
    h5f1.close()
    label1[label1==5]= 0
    label1[label1==9]= 0
    label1[label1==10]= 0
    label1[label1==12]= 0
    label1[label1==13]= 0
    label1[label1==11]= 5
    # unique_labels = np.unique(label1)
    # print("标签种类:", unique_labels)
    # print("标签个数:", len(unique_labels))

    # 图像2和标签2
    # h5f2 = h5py.File("code/case0028_slice060.h5", 'r')
    h5f2 = h5py.File("/home/<USER>/data/synapse/h5_2d/case0028_slice059.h5", 'r')
    image2 = h5f2['image'][:]
    label2 = h5f2['label'][:]
    h5f2.close()
    label2[label2==5]= 0
    label2[label2==9]= 0
    label2[label2==10]= 0
    label2[label2==12]= 0
    label2[label2==13]= 0
    label2[label2==11]= 5
    # unique_labels = np.unique(label2)
    # print("标签种类:", unique_labels)
    # print("标签个数:", len(unique_labels))
    # input()

    def crop_foreground(img, lbl=None, margin=10):
        """
        根据前景裁剪图像和标签
        img: 2D numpy array (归一化后的图像)
        lbl: 2D numpy array (标签，可选)
        margin: 在前景区域四周扩展的像素数
        """
        coords = np.argwhere(img > 0)   # 根据图像找前景
        
        if coords.size == 0:
            # 没有前景就直接返回原图
            return img, lbl
        
        # 获取 bounding box
        x0, y0 = coords.min(axis=0)
        x1, y1 = coords.max(axis=0) + 1
        
        # 扩展 margin
        x0 = max(x0 - margin, 0)
        y0 = max(y0 - margin, 0)
        x1 = min(x1 + margin, img.shape[0])
        y1 = min(y1 + margin, img.shape[1])
        
        # 裁剪
        img_cropped = img[x0:x1, y0:y1]
        lbl_cropped = lbl[x0:x1, y0:y1] if lbl is not None else None
        
        return img_cropped, lbl_cropped
    
    image1, label1 = crop_foreground(image1, label1, margin=10)
    image2, label2 = crop_foreground(image2, label2, margin=10)
    
    def preprocess_image(img):
        x, y = img.shape
        img_resized = zoom(img, (256 / x, 256 / y), order=0)
        img_rotated = np.rot90(img_resized, k=1)
        img_norm = (img_rotated - img_rotated.min()) / (img_rotated.max() - img_rotated.min())
        return img_norm

    def preprocess_label(lbl):
        x, y = lbl.shape
        lbl_resized = zoom(lbl, (256 / x, 256 / y), order=0)
        lbl_rotated = np.rot90(lbl_resized, k=1)
        return lbl_rotated.astype(np.uint8)

    image1_processed = preprocess_image(image1)
    image2_processed = preprocess_image(image2)
    label1_processed = preprocess_label(label1)
    label2_processed = preprocess_label(label2)
    
    print("Computing uncertainty for image2...")
    
    # 计算图像2的不确定性
    image2_tensor = torch.from_numpy(image2_processed.astype(np.float32)).unsqueeze(0).unsqueeze(0).to(device)
    _, uncertainty2 = monte_carlo_dropout(model, image2_tensor, num_samples=15)
    uncertainty2_np = uncertainty2.squeeze().numpy()
    
    print("Performing SLIC superpixel segmentation...")
    
    # 对两个图像进行SLIC分割
    segments1 = get_slic_superpixels(image1_processed, n_segments=50, compactness=0.2)
    segments2 = get_slic_superpixels(image2_processed, n_segments=50, compactness=0.2)
    
    print("Selecting high uncertainty regions...")
    
    # 选择图像2中不确定性最高的区域
    high_uncertainty_mask, selected_segments = select_high_uncertainty_regions(
        uncertainty2_np, segments2, top_k=6
    )
    
    # 确保连通性
    # final_mask = ensure_connectivity(high_uncertainty_mask, min_size=30)
    final_mask = high_uncertainty_mask

    # 创建超像素不确定度映射
    uncertainty_superpixel = create_uncertainty_superpixel_map(uncertainty2_np, segments2)

    # 执行双向CutMix（图像和标签）
    # 方向1: 从图像2复制到图像1 (红色边缘)
    cutmix_result_1to2 = cutmix_operation(image1_processed, image2_processed, final_mask)
    cutmix_label_1to2 = cutmix_labels(label1_processed, label2_processed, final_mask)

    # 方向2: 从图像1复制到图像2 (绿色边缘) - 使用相同区域的反向操作
    cutmix_result_2to1 = cutmix_operation(image2_processed, image1_processed, final_mask)
    cutmix_label_2to1 = cutmix_labels(label2_processed, label1_processed, final_mask)
    
    print("Creating visualization...")
    
    fig = plt.figure(figsize=(28, 20))

    # 设置整体布局 - 增加到5行7列来展示双向CutMix
    gs = fig.add_gridspec(5, 7, height_ratios=[1, 1, 1, 1, 1], width_ratios=[1, 1, 1, 1, 1, 1, 1],
                         hspace=0.3, wspace=0.15)
    
    # 第一行：原始图像和SLIC分割
    ax1 = fig.add_subplot(gs[0, 0])
    ax1.imshow(image1_processed, cmap='gray')
    ax1.set_title('Image 1 (Target)', fontsize=14, fontweight='bold')
    ax1.axis('off')
    
    ax2 = fig.add_subplot(gs[0, 1])
    ax2.imshow(mark_boundaries(image1_processed, segments1, color=(1, 1, 1), mode='thick'))
    ax2.set_title('SLIC Superpixels', fontsize=14, fontweight='bold')
    ax2.axis('off')
    
    ax3 = fig.add_subplot(gs[0, 2])
    ax3.imshow(image2_processed, cmap='gray')
    ax3.set_title('Image 2 (Source)', fontsize=14, fontweight='bold')
    ax3.axis('off')
    
    ax4 = fig.add_subplot(gs[0, 3])
    ax4.imshow(mark_boundaries(image2_processed, segments2, color=(1, 0, 0), mode='thick'))
    ax4.set_title('SLIC Superpixels', fontsize=14, fontweight='bold')
    ax4.axis('off')
    
    ax5 = fig.add_subplot(gs[0, 4])
    im_unc = ax5.imshow(uncertainty2_np, cmap='hot', interpolation='bilinear')
    ax5.set_title('Entropy Uncertainty', fontsize=14, fontweight='bold')
    ax5.axis('off')
    cbar = plt.colorbar(im_unc, ax=ax5, shrink=0.8)
    cbar.set_label('Uncertainty', fontsize=12)

    # 添加超像素不确定度图
    ax6 = fig.add_subplot(gs[0, 5])
    uncertainty_superpixel = create_uncertainty_superpixel_map(uncertainty2_np, segments2)
    im_sup_unc = ax6.imshow(uncertainty_superpixel, cmap='viridis', interpolation='nearest')
    # 使用contour直接在segments上绘制边界线，避免空隙
    unique_segments = np.unique(segments2)
    for seg_id in unique_segments:
        if seg_id > 0:  # 跳过背景
            mask = (segments2 == seg_id).astype(float)
            ax6.contour(mask, levels=[0.5], colors='white', linewidths=0.8, alpha=0.8)
    ax6.set_title('Superpixel Uncertainty', fontsize=14, fontweight='bold')
    ax6.axis('off')
    cbar2 = plt.colorbar(im_sup_unc, ax=ax6, shrink=0.8)
    cbar2.set_label('Avg Uncertainty', fontsize=12)

    # 第二行：标签可视化
    colormap = create_label_colormap()

    ax7 = fig.add_subplot(gs[1, 0])
    ax7.imshow(label1_processed, cmap=colormap, vmin=0, vmax=8)
    ax7.set_title('Label 1 (Target)', fontsize=14, fontweight='bold')
    ax7.axis('off')

    ax8 = fig.add_subplot(gs[1, 1])
    label1_overlay = visualize_label_overlay(image1_processed, label1_processed)
    ax8.imshow(label1_overlay)
    ax8.set_title('Image 1 + Label Overlay', fontsize=14, fontweight='bold')
    ax8.axis('off')

    ax9 = fig.add_subplot(gs[1, 2])
    ax9.imshow(label2_processed, cmap=colormap, vmin=0, vmax=8)
    ax9.set_title('Label 2 (Source)', fontsize=14, fontweight='bold')
    ax9.axis('off')

    ax10 = fig.add_subplot(gs[1, 3])
    label2_overlay = visualize_label_overlay(image2_processed, label2_processed)
    ax10.imshow(label2_overlay)
    ax10.set_title('Image 2 + Label Overlay', fontsize=14, fontweight='bold')
    ax10.axis('off')

    ax11 = fig.add_subplot(gs[1, 4])
    ax11.imshow(cutmix_label_1to2, cmap=colormap, vmin=0, vmax=8)
    ax11.set_title('CutMix Label 1→2', fontsize=14, fontweight='bold')
    ax11.axis('off')

    # 添加CutMix后的图像+标签叠加
    ax12 = fig.add_subplot(gs[1, 5])
    cutmix_overlay_1to2 = visualize_label_overlay(cutmix_result_1to2, cutmix_label_1to2)
    ax12.imshow(cutmix_overlay_1to2)
    ax12.set_title('CutMix 1→2 + Label', fontsize=14, fontweight='bold')
    ax12.axis('off')

    # 添加反向CutMix结果
    ax13 = fig.add_subplot(gs[1, 6])
    cutmix_overlay_2to1 = visualize_label_overlay(cutmix_result_2to1, cutmix_label_2to1)
    ax13.imshow(cutmix_overlay_2to1)
    ax13.set_title('CutMix 2→1 + Label', fontsize=14, fontweight='bold')
    ax13.axis('off')

    # 第三行：双向CutMix过程
    ax14 = fig.add_subplot(gs[2, 0])
    # 显示选中的超像素
    selected_vis = np.zeros_like(segments2)
    for seg_id in selected_segments:
        selected_vis[segments2 == seg_id] = 1
    ax14.imshow(image2_processed, cmap='gray', alpha=0.7)
    ax14.imshow(selected_vis, cmap='Reds', alpha=0.6)
    ax14.set_title('Selected High\nUncertainty Superpixels', fontsize=14, fontweight='bold')
    ax14.axis('off')

    ax15 = fig.add_subplot(gs[2, 1])
    ax15.imshow(final_mask, cmap='Reds')
    ax15.set_title('Connected Regions\nMask', fontsize=14, fontweight='bold')
    ax15.axis('off')

    # 双向箭头指示
    ax16 = fig.add_subplot(gs[2, 2])
    ax16.text(0.5, 0.7, '→', fontsize=40, ha='center', va='center',
             transform=ax16.transAxes, fontweight='bold', color='lime')  # 2→1用亮绿色
    ax16.text(0.5, 0.3, '←', fontsize=40, ha='center', va='center',
             transform=ax16.transAxes, fontweight='bold', color='red')   # 1→2用红色
    ax16.set_title('Bidirectional\nCutMix', fontsize=14, fontweight='bold')
    ax16.axis('off')

    # CutMix结果1: 图像2→图像1 (内部绿色边缘，外框红色)
    ax17 = fig.add_subplot(gs[2, 3])
    ax17.imshow(cutmix_result_1to2, cmap='gray')
    ax17.contour(final_mask, levels=[0.5], colors='red', linewidths=2)  # 内部边缘用亮绿色
    rect = patches.Rectangle(
        (0, 0), 255, 255, linewidth=4, edgecolor='lime', facecolor='none'
    )
    ax17.add_patch(rect)
    ax17.axis('off')
    ax17.set_title('CutMix 2→1', fontsize=14, fontweight='bold')


    # CutMix结果2: 图像1→图像2 (内部红色边缘，外框绿色)
    ax18 = fig.add_subplot(gs[2, 4])
    ax18.imshow(cutmix_result_2to1, cmap='gray')
    ax18.contour(final_mask, levels=[0.5], colors='lime', linewidths=2)  # 内部边缘用红色
    ax18.axis('off')
    ax18.set_title('CutMix 1→2', fontsize=14, fontweight='bold')

    # 显示选中超像素的不确定度分布
    ax19 = fig.add_subplot(gs[2, 5])
    selected_uncertainty = uncertainty_superpixel.copy()
    selected_uncertainty[~final_mask] = 0
    ax19.imshow(selected_uncertainty, cmap='plasma', interpolation='nearest')
    # 使用contour直接在segments上绘制边界线，避免空隙
    unique_segments = np.unique(segments2)
    for seg_id in unique_segments:
        if seg_id > 0:  # 跳过背景
            mask = (segments2 == seg_id).astype(float)
            ax19.contour(mask, levels=[0.5], colors='white', linewidths=0.8, alpha=0.8)
    ax19.set_title('Selected Regions\nUncertainty', fontsize=14, fontweight='bold')
    ax19.axis('off')

    # 添加双向对比
    ax20 = fig.add_subplot(gs[2, 6])
    # 并排显示两个CutMix结果
    bidirectional_comparison = np.hstack([cutmix_result_1to2, cutmix_result_2to1])
    ax20.imshow(bidirectional_comparison, cmap='gray')
    ax20.axvline(x=256, color='yellow', linestyle='--', linewidth=2)
    ax20.set_title('Bidirectional\nComparison', fontsize=14, fontweight='bold')
    ax20.axis('off')
    
    # 第四行：双向对比
    ax21 = fig.add_subplot(gs[3, :2])
    # 显示原始图像1和CutMix结果1 (2→1) - 红色外边框
    comparison_1 = np.hstack([image1_processed, cutmix_result_1to2])
    ax21.imshow(comparison_1, cmap='gray')
    ax21.axvline(x=256, color='lime', linestyle='--', linewidth=2)  # 分割线用亮绿色
    # 添加红色外边框
    for spine in ax21.spines.values():
        spine.set_edgecolor('red')
        spine.set_linewidth(4)
        spine.set_visible(True)
    ax21.set_xticks([])
    ax21.set_yticks([])
    ax21.set_title('Image 1: Before vs After CutMix (2→1, Red Frame)', fontsize=14, fontweight='bold')

    ax22 = fig.add_subplot(gs[3, 2:4])
    # 显示原始图像2和CutMix结果2 (1→2) - 绿色外边框
    comparison_2 = np.hstack([image2_processed, cutmix_result_2to1])
    ax22.imshow(comparison_2, cmap='gray')
    ax22.axvline(x=256, color='red', linestyle='--', linewidth=2)  # 分割线用红色
    # 添加亮绿色外边框
    for spine in ax22.spines.values():
        spine.set_edgecolor('lime')
        spine.set_linewidth(4)
        spine.set_visible(True)
    ax22.set_xticks([])
    ax22.set_yticks([])
    ax22.set_title('Image 2: Before vs After CutMix (1→2, Green Frame)', fontsize=14, fontweight='bold')

    # 标签对比
    ax23 = fig.add_subplot(gs[3, 4])
    label_comparison_1 = np.hstack([label1_processed, cutmix_label_1to2])
    ax23.imshow(label_comparison_1, cmap=colormap, vmin=0, vmax=8)
    ax23.axvline(x=256, color='white', linestyle='--', linewidth=2)
    ax23.set_title('Label 1: Before vs After', fontsize=14, fontweight='bold')
    ax23.axis('off')

    ax24 = fig.add_subplot(gs[3, 5])
    label_comparison_2 = np.hstack([label2_processed, cutmix_label_2to1])
    ax24.imshow(label_comparison_2, cmap=colormap, vmin=0, vmax=8)
    ax24.axvline(x=256, color='white', linestyle='--', linewidth=2)
    ax24.set_title('Label 2: Before vs After', fontsize=14, fontweight='bold')
    ax24.axis('off')

    # 第五行：统计信息
    ax25 = fig.add_subplot(gs[4, :])
    # 计算标签统计
    unique_labels1 = np.unique(label1_processed)
    unique_labels2 = np.unique(label2_processed)
    unique_labels_cutmix_1to2 = np.unique(cutmix_label_1to2)
    unique_labels_cutmix_2to1 = np.unique(cutmix_label_2to1)
    copied_labels = np.unique(label2_processed[final_mask])

    stats_text = f"""Bidirectional CutMix Statistics:

Image & Uncertainty:
• Total superpixels in Image 2: {len(np.unique(segments2)) - 1}
• Selected high uncertainty superpixels: {len(selected_segments)}
• Final connected regions: {len(np.unique(label(final_mask))) - 1}
• Copied area: {final_mask.sum()} pixels ({100*final_mask.sum()/final_mask.size:.1f}%)
• Avg uncertainty in copied regions: {uncertainty2_np[final_mask].mean():.4f}

Label Information:
• Original Label 1 classes: {len(unique_labels1)} ({list(unique_labels1)})
• Original Label 2 classes: {len(unique_labels2)} ({list(unique_labels2)})
• CutMix 2→1 result classes: {len(unique_labels_cutmix_1to2)} ({list(unique_labels_cutmix_1to2)})
• CutMix 1→2 result classes: {len(unique_labels_cutmix_2to1)} ({list(unique_labels_cutmix_2to1)})
• Copied label classes: {len(copied_labels)} ({list(copied_labels)})

Bidirectional Process:
1. Load images and corresponding segmentation labels
2. Perform SLIC superpixel segmentation on both images
3. Compute entropy-based uncertainty for Image 2
4. Select top-k superpixels with highest uncertainty
5. Apply bidirectional CutMix:
   - Direction 1: Copy from Image 2 to Image 1 (Green internal edge, Red frame)
   - Direction 2: Copy from Image 1 to Image 2 (Red internal edge, Green frame)
6. Both images and labels are processed simultaneously"""

    ax25.text(0.05, 0.95, stats_text, transform=ax25.transAxes, fontsize=10,
              verticalalignment='top', fontfamily='monospace',
              bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))
    ax25.axis('off')
    
    plt.suptitle('Bidirectional SLIC-based Uncertainty-guided CutMix for Medical Image Segmentation',
                 fontsize=18, fontweight='bold', y=0.98)

    # 创建imgs目录
    os.makedirs('imgs', exist_ok=True)

    # 保存高质量图像
    output_path = 'imgs/bidirectional_slic_uncertainty_cutmix_paper_figure.png'
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"Paper figure saved to: {output_path}")

    # 保存数据
    cutmix_data = {
        'image1_original': image1_processed,
        'image2_original': image2_processed,
        'label1_original': label1_processed,
        'label2_original': label2_processed,
        'uncertainty_map': uncertainty2_np,
        'segments1': segments1,
        'segments2': segments2,
        'selected_mask': final_mask,
        'cutmix_result_1to2': cutmix_result_1to2,
        'cutmix_label_1to2': cutmix_label_1to2,
        'cutmix_result_2to1': cutmix_result_2to1,
        'cutmix_label_2to1': cutmix_label_2to1,
        'selected_segments': selected_segments
    }

    np.savez('imgs/bidirectional_slic_cutmix_data.npz', **cutmix_data)
    print("Bidirectional CutMix data saved to: imgs/bidirectional_slic_cutmix_data.npz")

    # # 单独保存各种图片（不包含标题）
    # print("Saving individual images...")

    # # 保存原始图像
    # save_simple_image(image1_processed, 'imgs/image1_original.png')
    # save_simple_image(image2_processed, 'imgs/image2_original.png')

    # # 保存标签图像
    # colormap = create_label_colormap()
    # save_simple_image(label1_processed, 'imgs/label1_original.png', cmap=colormap)
    # save_simple_image(label2_processed, 'imgs/label2_original.png', cmap=colormap)

    # # 保存不确定性图
    # save_uncertainty_image(uncertainty2_np, 'imgs/uncertainty_map.png')

    # # 保存超像素分割图
    # save_superpixel_image(image1_processed, segments1, 'imgs/image1_superpixels.png')
    # save_superpixel_image(image2_processed, segments2, 'imgs/image2_superpixels.png')

    # # 保存超像素不确定性图
    # save_simple_image(uncertainty_superpixel, 'imgs/superpixel_uncertainty.png', cmap='viridis')

    # # 保存选中区域掩码
    # save_simple_image(final_mask.astype(float), 'imgs/selected_mask.png', cmap='Reds')

    # # 保存CutMix结果
    # save_image_only(cutmix_result_1to2, final_mask, 'lime', 'red', 'imgs/cutmix_2to1_result.png')
    # save_image_only(cutmix_result_2to1, final_mask, 'red', 'lime', 'imgs/cutmix_1to2_result.png')

    # # 保存CutMix标签结果
    # save_simple_image(cutmix_label_1to2, 'imgs/cutmix_label_2to1.png', cmap=colormap)
    # save_simple_image(cutmix_label_2to1, 'imgs/cutmix_label_1to2.png', cmap=colormap)

    # # 保存叠加图像
    # label1_overlay = visualize_label_overlay(image1_processed, label1_processed)
    # label2_overlay = visualize_label_overlay(image2_processed, label2_processed)
    # cutmix_overlay_1to2 = visualize_label_overlay(cutmix_result_1to2, cutmix_label_1to2)
    # cutmix_overlay_2to1 = visualize_label_overlay(cutmix_result_2to1, cutmix_label_2to1)

    # save_simple_image(label1_overlay, 'imgs/image1_label_overlay.png')
    # save_simple_image(label2_overlay, 'imgs/image2_label_overlay.png')
    # save_simple_image(cutmix_overlay_1to2, 'imgs/cutmix_2to1_overlay.png')
    # save_simple_image(cutmix_overlay_2to1, 'imgs/cutmix_1to2_overlay.png')

    print("All individual images saved successfully!")
    # plt.show()

if __name__ == "__main__":
    create_paper_figure()
