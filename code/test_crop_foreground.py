#!/usr/bin/env python3
"""
测试前景裁剪功能的简单脚本
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.ndimage import zoom

def crop_foreground(img, threshold=0.01):
    """
    裁剪图像的前景区域，去除四周的黑色背景
    
    Args:
        img: 输入图像 (2D numpy array)
        threshold: 前景阈值，低于此值的像素被认为是背景
    
    Returns:
        cropped_img: 裁剪后的图像
        bbox: 边界框 (top, bottom, left, right)
    """
    # 找到非零（前景）像素的位置
    foreground_mask = img > threshold
    
    # 找到前景区域的边界
    rows = np.any(foreground_mask, axis=1)
    cols = np.any(foreground_mask, axis=0)
    
    if not np.any(rows) or not np.any(cols):
        # 如果没有前景，返回原图
        return img, (0, img.shape[0], 0, img.shape[1])
    
    top, bottom = np.where(rows)[0][[0, -1]]
    left, right = np.where(cols)[0][[0, -1]]
    
    # 添加一些边距，避免裁剪过紧
    margin = 5
    top = max(0, top - margin)
    bottom = min(img.shape[0], bottom + margin + 1)
    left = max(0, left - margin)
    right = min(img.shape[1], right + margin + 1)
    
    cropped_img = img[top:bottom, left:right]
    return cropped_img, (top, bottom, left, right)

def create_test_image():
    """创建一个测试图像，中间有内容，四周是黑色"""
    img = np.zeros((256, 256))
    
    # 在中间创建一个圆形区域
    center_x, center_y = 128, 128
    radius = 80
    y, x = np.ogrid[:256, :256]
    mask = (x - center_x)**2 + (y - center_y)**2 <= radius**2
    img[mask] = 0.8
    
    # 添加一些噪声
    noise = np.random.normal(0, 0.1, (256, 256))
    img[mask] += noise[mask]
    
    # 确保值在[0,1]范围内
    img = np.clip(img, 0, 1)
    
    return img

def test_crop_foreground():
    """测试前景裁剪功能"""
    # 创建测试图像
    test_img = create_test_image()
    
    # 应用前景裁剪
    cropped_img, bbox = crop_foreground(test_img)
    top, bottom, left, right = bbox
    
    # 显示结果
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # 原始图像
    axes[0].imshow(test_img, cmap='gray')
    axes[0].set_title(f'Original Image\nSize: {test_img.shape}')
    axes[0].axis('off')
    
    # 显示裁剪区域
    axes[1].imshow(test_img, cmap='gray')
    axes[1].axhline(y=top, color='red', linestyle='--', linewidth=2)
    axes[1].axhline(y=bottom-1, color='red', linestyle='--', linewidth=2)
    axes[1].axvline(x=left, color='red', linestyle='--', linewidth=2)
    axes[1].axvline(x=right-1, color='red', linestyle='--', linewidth=2)
    axes[1].set_title(f'Crop Region\nBBox: ({top}, {bottom}, {left}, {right})')
    axes[1].axis('off')
    
    # 裁剪后的图像
    axes[2].imshow(cropped_img, cmap='gray')
    axes[2].set_title(f'Cropped Image\nSize: {cropped_img.shape}')
    axes[2].axis('off')
    
    plt.tight_layout()
    plt.savefig('test_crop_foreground_result.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"Original image size: {test_img.shape}")
    print(f"Cropped image size: {cropped_img.shape}")
    print(f"Bounding box: top={top}, bottom={bottom}, left={left}, right={right}")
    print(f"Size reduction: {test_img.size} -> {cropped_img.size} pixels")
    print(f"Compression ratio: {cropped_img.size / test_img.size:.2f}")

if __name__ == "__main__":
    test_crop_foreground()
